<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Services\S3Service;
use Illuminate\Support\Facades\Validator;

class ImageController extends Controller
{
    protected $s3Service;

    public function __construct(S3Service $s3Service)
    {
        $this->s3Service = $s3Service;
    }

    /**
     * Upload single image to S3
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function uploadImage(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
            'folder' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $folder = $request->input('folder', 'uploads');
        $imagePath = $this->s3Service->uploadFile($request->file('image'), $folder);

        if ($imagePath) {
            return response()->json([
                'success' => true,
                'message' => 'Image uploaded successfully',
                'data' => [
                    'path' => $imagePath,
                    'url' => $this->s3Service->getFileUrl($imagePath)
                ]
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Failed to upload image'
        ], 500);
    }

    /**
     * Upload multiple images to S3
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function uploadMultipleImages(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'images' => 'required|array',
            'images.*' => 'image|mimes:jpeg,png,jpg,gif|max:2048',
            'folder' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $folder = $request->input('folder', 'uploads');
        $uploadedFiles = $this->s3Service->uploadMultipleFiles($request->file('images'), $folder);

        if (!empty($uploadedFiles)) {
            $filesWithUrls = array_map(function($path) {
                return [
                    'path' => $path,
                    'url' => $this->s3Service->getFileUrl($path)
                ];
            }, $uploadedFiles);

            return response()->json([
                'success' => true,
                'message' => 'Images uploaded successfully',
                'data' => $filesWithUrls
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Failed to upload images'
        ], 500);
    }

    /**
     * Delete image from S3
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteImage(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'path' => 'required|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $deleted = $this->s3Service->deleteFile($request->input('path'));

        if ($deleted) {
            return response()->json([
                'success' => true,
                'message' => 'Image deleted successfully'
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Failed to delete image'
        ], 500);
    }

    /**
     * Get image URL from S3
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getImageUrl(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'path' => 'required|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $path = $request->input('path');
        
        if ($this->s3Service->fileExists($path)) {
            $url = $this->s3Service->getFileUrl($path);
            
            return response()->json([
                'success' => true,
                'data' => [
                    'path' => $path,
                    'url' => $url,
                    'exists' => true
                ]
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Image not found',
            'data' => [
                'path' => $path,
                'exists' => false
            ]
        ], 404);
    }

    /**
     * List images in a specific folder
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function listImages(Request $request)
    {
        $folder = $request->input('folder', 'products');
        
        try {
            $files = \Storage::disk('s3')->files($folder);
            $images = [];
            
            foreach ($files as $file) {
                // Only include image files
                $extension = pathinfo($file, PATHINFO_EXTENSION);
                if (in_array(strtolower($extension), ['jpg', 'jpeg', 'png', 'gif', 'webp'])) {
                    $images[] = [
                        'path' => $file,
                        'url' => $this->s3Service->getFileUrl($file),
                        'name' => basename($file),
                        'size' => $this->s3Service->getFileSize($file)
                    ];
                }
            }
            
            return response()->json([
                'success' => true,
                'data' => $images,
                'count' => count($images)
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to list images',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
